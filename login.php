<?php
session_start([
    'cookie_secure'   => true,       // Only send cookie over HTTPS
    'cookie_httponly' => true,       // <PERSON><PERSON> cannot access the cookie
    'cookie_samesite' => 'Strict'    // Prevent CSRF in modern browsers
]);

include 'connection/dbconnect.php';

// Check if essential session variables are set
if (isset($_SESSION['id'], $_SESSION['accountType'])) {
    // Ensure the user ID is a positive integer
    $id = (int) $_SESSION['id'];
    if ($id <= 0) {
        session_unset();
        session_destroy();
        header("Location: login.php");
        exit();
    }

    // Retrieve session variables
    $accountType = $_SESSION['accountType'];
    $status = $_SESSION['accountStatus'] ?? null;
    $operator = $_SESSION['operator'] ?? null;

    // Redirect based on account status
    if ($status === "Incomplete") {
        header('Location: auth/register-other-details.php');
        exit();
    } elseif ($status === "pending" || $status === "ready") {
        header('Location: auth/register-complete.php');
        exit();
    } else {
        // Redirect based on account type
        if ($accountType === "admin" || $accountType === "mtho") {
            header('Location: account/employee/admin/home.php');
            exit();
        } elseif ($accountType === "treasurer") {
            header('Location: account/employee/treasurer/home.php');
            exit();
        } else {
            // Redirect based on operator type
            if ($operator === "Tour operator") {
                header('Location: account/operator/tour/home.php');
                exit();
            } elseif ($operator === "Resort operator") {
                header('Location: account/operator/resort/home.php');
                exit();
            } elseif ($operator === "Boat operator") {
                header('Location: account/operator/boat/home.php');
                exit();
            }
        }
    }
}

// Generate a CSRF token if it doesn't exist or has expired
if (empty($_SESSION['csrf_token']) || time() > ($_SESSION['csrf_token_expiration'] ?? 0)) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    $_SESSION['csrf_token_expiration'] = time() + 3600; // 1-hour validity
}

// Initialize notice variable
$setNotice = '';

// Retrieve the system maintenance status using a try/catch block
try {
    $sql = 'SELECT status_maintenance FROM system LIMIT 1';
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($row && isset($row['status_maintenance'])) {
        // Cast the status to integer for a strict comparison
        if ((int)$row['status_maintenance'] !== 0) {
            $setNotice = '
            <div class="flex items-center p-4 my-4 text-sm text-red-800 rounded-lg bg-red-200" role="alert">
                <svg class="shrink-0 inline w-4 h-4 me-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
                </svg>
                <span class="sr-only">Info</span>
                <div>
                    <span class="font-medium">Notice!</span> Notice: Our system is currently under maintenance. We will be back shortly. Thank you for your patience!
                </div>
            </div>
            ';
        }
    } else {
        error_log("System maintenance status not found in the database.");
    }
} catch (PDOException $e) {
    error_log("Error executing maintenance query: " . $e->getMessage());
    die("An error occurred. Please try again later.");
}

// $setNotice now holds the maintenance notice (if applicable) and can be used later in your HTML output.
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Calaguas Booking | Login</title>
    <link rel="icon" type="image/x-icon" href="public/img/Logo.png" />
    <script src="https://kit.fontawesome.com/1ee5dd1d52.js" crossorigin="anonymous"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet" />
</head>

<body class="bg-gray-50 min-h-screen flex flex-col">
    <div class="flex flex-col items-center justify-center flex-grow py-4">
        <div class="w-full max-w-sm p-6 bg-white rounded-lg shadow-lg">
            <div class="text-center mb-6">
                <img src="components/img/Logo.png" alt="VNLogo" class="w-32 mx-auto mb-4" draggable="false" />
                <h2 class="text-xl font-bold text-gray-800">Welcome Back!</h2>
                <p class="text-gray-600 mt-1 text-sm">Sign in to your account to continue</p>
            </div>

            <?= $setNotice; ?>

            <form action="inc.login.php" method="POST" class="space-y-4">
                <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">

                <div>
                    <label for="username" class="block mb-1 text-sm font-medium text-gray-700">Username</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                            <svg class="w-4 h-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                            </svg>
                        </div>
                        <input
                            type="text"
                            id="username"
                            name="username"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-9 p-2"
                            placeholder="Enter your username"
                            required
                        />
                    </div>
                </div>

                <div>
                    <label for="password" class="block mb-1 text-sm font-medium text-gray-700">Password</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                            <svg class="w-4 h-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
                            </svg>
                        </div>
                        <input
                            type="password"
                            id="password"
                            name="password"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-9 p-2"
                            placeholder="Enter your password"
                            required
                        />
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input
                            type="checkbox"
                            id="showPassword"
                            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                        />
                        <label for="showPassword" class="ms-2 text-sm font-medium text-gray-700">Show Password</label>
                    </div>
                    <a href="forgotpassword.php" class="text-sm font-medium text-blue-600 hover:underline">Forgot Password?</a>
                </div>

                <button
                    type="submit"
                    name="loginBtn"
                    class="w-full text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2.5 transition-colors"
                >
                    Sign In
                </button>

                <div class="relative flex items-center justify-center py-1">
                    <div class="flex-grow border-t border-gray-300"></div>
                    <span class="flex-shrink mx-3 text-gray-600 text-xs">OR</span>
                    <div class="flex-grow border-t border-gray-300"></div>
                </div>

                <a
                    href="register.php"
                    class="w-full inline-flex justify-center items-center text-center bg-white border border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2.5 transition-colors"
                >
                    <svg class="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M19 7.5v3m0 0v3m0-3h3m-3 0h-3m-2.25-4.125a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zM4 19.235v-.11a6.375 6.375 0 0112.75 0v.109A12.318 12.318 0 0110.374 21c-2.331 0-4.512-.645-6.374-1.766z" />
                    </svg>
                    Create New Account
                </a>

                <div class="text-center text-xs text-gray-600 mt-3">
                    <p>
                        Need to renew your account?
                        <a href="#" class="font-medium text-blue-600 hover:underline">Renew here</a>
                    </p>
                </div>
            </form>
        </div>
    </div>

    <footer class="bg-blue-600 text-white p-4 mt-4 shadow-inner">
        <div class="container mx-auto text-center">
            <p class="text-xs">Copyright © 2025 Municipality of Vinzons, Camarines Norte | All Rights Reserved.</p>
            <p class="text-xs mt-1">Municipal Tourism and Heritage Operations</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>

    <script>
        // Toggle password visibility
        document.getElementById('showPassword').addEventListener('change', function() {
            const passwordField = document.getElementById('password');
            passwordField.type = this.checked ? 'text' : 'password';
        });
    </script>

    <?php if (isset($_SESSION['error'])): ?>
        <script>
            Swal.fire({
                icon: 'error',
                title: 'Login Failed',
                text: '<?php echo htmlspecialchars($_SESSION['error'], ENT_QUOTES, 'UTF-8'); ?>',
                confirmButtonText: 'Try Again',
                confirmButtonColor: '#3b82f6',
            });
        </script>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['success'])): ?>
        <script>
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: '<?php echo htmlspecialchars($_SESSION['success'], ENT_QUOTES, 'UTF-8'); ?>',
                confirmButtonColor: '#3b82f6'
            });
        </script>
        <?php unset($_SESSION['success']); ?>
    <?php endif; ?>
</body>

</html>