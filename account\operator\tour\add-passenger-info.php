<?php
ob_start();
require '_header.php';
$getBookingId = isset($_GET['id']) ? (int) $_GET['id'] : 0;
$statusBooking = "draft";
$statusBooking2 = "declined";

if (empty($getBookingId)) {
    header("Location: transaction-draft.php");
    exit;
}

$bookingDetails = getBookingDetails($pdo, $getBookingId);
$disContactPassenger = getDistinctTourist($pdo, "contact_number", $getBookingId);
$disAddressPassenger = getDistinctTourist($pdo, "address", $getBookingId);

if ($bookingDetails['booking_status'] != $statusBooking && $bookingDetails['booking_status'] != $statusBooking2) {
    header("Location: transaction-draft.php");
    exit;
} else {

    if ($bookingDetails !== null) {
        $extname = $bookingDetails['extname'] ?? ''; // Use null coalescing operator for defaults
        $middlename = $bookingDetails['middlename'] ?? '';
        $operatorName = trim($bookingDetails['firstname'] . ' ' . $middlename . ' ' . $bookingDetails['lastname'] . ' ' . $extname);
        $designationTour = $bookingDetails['tour_operator_designation'];
        $resortName = $bookingDetails['resort_operator_designation'];
        $referenceNumber = $bookingDetails['referenceNum'];
        $boatName = $bookingDetails['boatName'];
        $portName = $bookingDetails['portName'];
        $checkIn = $bookingDetails['check_in_date'];
        $checkout = $bookingDetails['check_out_date'];
        $adultCount = $bookingDetails['total_adults'];
        $childrenCount = $bookingDetails['total_children'];
    }

    if ($name !== $operatorName) {
        header("Location: transaction-draft.php");
        exit;
    }
}

// Get Count from inserted tourist info
$counts = getTouristAndCrewCounts($pdo, $getBookingId);
$actual_adultCount = $counts['adults'];
$actual_childrenCount = $counts['children'];
$actual_crewCount = $counts['crewTts'];

// Check if there's at least 1 adult and 1 crew member
if ($actual_adultCount < 1 || $actual_crewCount < 1) {
    $paymentBtn = 'disabled';
} else {
    $paymentBtn = "";
}

ob_end_flush();
?>

<div class="p-4 sm:ml-64">
    <div class="mt-16"> <!-- Adjusted for top navbar -->
        <!-- Main Content Section -->
        <div class="border p-4 rounded-lg shadow-lg bg-white mb-6">
        <!-- Breadcrumb -->
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                <li class="inline-flex items-center">
                    <a href="home.php" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                        <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                        </svg>
                        Home
                    </a>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2"><?= $bookingDetails['booking_status'] == 'draft' ? 'Draft' : 'Declined' ?></span>
                    </div>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2">Passenger Info</span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- Status Messages -->
         <div class="mt-4">
            <?php if ($bookingDetails['booking_status'] == 'declined'): ?>
                <!-- Notification for declined booking -->
                <div class="flex p-4 mb-4 text-amber-800 rounded-lg bg-amber-50 border border-amber-200" role="alert">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-amber-600"></i>
                    </div>
                    <div class="ml-3 text-sm font-medium text-amber-800">
                        <span class="font-semibold">This booking was declined.</span> You can edit passenger information here and then return to the booking edit page to update other details.
                    </div>
                </div>
            <?php endif; ?>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="flex p-4 mb-4 text-green-800 rounded-lg bg-green-50 border border-green-200" role="alert">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-green-600"></i>
                    </div>
                    <div class="ml-3 text-sm font-medium text-green-800">
                        <?= htmlspecialchars($_SESSION['success']); ?>
                    </div>
                    <button type="button" class="ml-auto -mx-1.5 -my-1.5 bg-green-50 text-green-500 rounded-lg focus:ring-2 focus:ring-green-400 p-1.5 hover:bg-green-200 inline-flex items-center justify-center h-8 w-8" data-dismiss-target="[role='alert']" aria-label="Close">
                        <span class="sr-only">Close</span>
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                    </button>
                </div>
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="flex p-4 mb-4 text-red-800 rounded-lg bg-red-50 border border-red-200" role="alert">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-circle text-red-600"></i>
                    </div>
                    <div class="ml-3 text-sm font-medium text-red-800">
                        <?= htmlspecialchars($_SESSION['error']); ?>
                    </div>
                    <button type="button" class="ml-auto -mx-1.5 -my-1.5 bg-red-50 text-red-500 rounded-lg focus:ring-2 focus:ring-red-400 p-1.5 hover:bg-red-200 inline-flex items-center justify-center h-8 w-8" data-dismiss-target="[role='alert']" aria-label="Close">
                        <span class="sr-only">Close</span>
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                    </button>
                </div>
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>
        </div>

        <!-- Header Section with Icon -->
        <div class="flex items-center mt-6">
            <div class="bg-blue-100 p-2 rounded-lg mr-3">
                <i class="fas fa-users text-blue-600 text-xl"></i>
            </div>
            <div>
                <?php if ($bookingDetails['booking_status'] == 'declined'): ?>
                <h2 class="text-xl font-bold text-gray-900">Edit Passenger Information</h2>
                <p class="text-sm text-gray-600">Modify passenger details for your declined booking</p>
                <?php else: ?>
                <h2 class="text-xl font-bold text-gray-900">Passenger Information</h2>
                <p class="text-sm text-gray-600">Add tourist and crew information here</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Booking Summary Card -->
        <div class="mt-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100 p-4 shadow-sm">
            <div class="flex items-center mb-3">
                <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                <h3 class="text-md font-semibold text-blue-800">Booking Summary</h3>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-white rounded-lg p-3 shadow-sm border border-blue-100">
                    <p class="text-xs font-medium text-gray-500 uppercase">Reference</p>
                    <p class="text-sm font-semibold text-gray-800"><?= htmlspecialchars($referenceNumber, ENT_QUOTES, 'UTF-8'); ?></p>
                </div>
                <div class="bg-white rounded-lg p-3 shadow-sm border border-blue-100">
                    <p class="text-xs font-medium text-gray-500 uppercase">Destination</p>
                    <p class="text-sm font-semibold text-gray-800"><?= htmlspecialchars($resortName, ENT_QUOTES, 'UTF-8'); ?></p>
                </div>
                <div class="bg-white rounded-lg p-3 shadow-sm border border-blue-100">
                    <p class="text-xs font-medium text-gray-500 uppercase">Travel Dates</p>
                    <p class="text-sm font-semibold text-gray-800"><?= (new DateTime(htmlspecialchars($checkIn, ENT_QUOTES, 'UTF-8')))->format('M d') ?> - <?= (new DateTime(htmlspecialchars($checkout, ENT_QUOTES, 'UTF-8')))->format('M d, Y') ?></p>
                </div>
            </div>
        </div>

        <!-- Passenger Count Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
            <!-- Adults Section -->
            <div class="bg-white border border-blue-200 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow-md">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="flex items-center">
                            <h3 class="text-sm font-semibold text-blue-700">Adults</h3>
                            <span class="ml-2 bg-red-100 text-red-800 text-xs font-medium px-2 py-0.5 rounded-full">Required</span>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Age 9 and above (min: 1)</p>
                    </div>
                    <div class="bg-blue-100 p-2 rounded-full">
                        <i class="fas fa-user text-blue-600"></i>
                    </div>
                </div>
                <div class="mt-3 flex items-end">
                    <span class="text-2xl font-bold text-blue-700"><?= $actual_adultCount; ?></span>
                    <span class="text-sm text-gray-500 ml-1 mb-1">/ <?= $adultCount; ?></span>
                    <div class="ml-auto">
                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                            <div class="bg-blue-600 h-2.5 rounded-full" style="width: <?= ($actual_adultCount / max(1, $adultCount)) * 100 ?>%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Children Section -->
            <div class="bg-white border border-green-200 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow-md">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-semibold text-green-700">Children</h3>
                        <p class="text-xs text-gray-500 mt-1">Age 8 and below</p>
                    </div>
                    <div class="bg-green-100 p-2 rounded-full">
                        <i class="fas fa-child text-green-600"></i>
                    </div>
                </div>
                <div class="mt-3 flex items-end">
                    <span class="text-2xl font-bold text-green-700"><?= $actual_childrenCount; ?></span>
                    <span class="text-sm text-gray-500 ml-1 mb-1">/ <?= $childrenCount; ?></span>
                    <div class="ml-auto">
                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                            <div class="bg-green-600 h-2.5 rounded-full" style="width: <?= ($actual_childrenCount / max(1, $childrenCount)) * 100 ?>%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Crew Section -->
            <div class="bg-white border border-amber-200 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow-md">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="flex items-center">
                            <h3 class="text-sm font-semibold text-amber-700">Crew</h3>
                            <span class="ml-2 bg-red-100 text-red-800 text-xs font-medium px-2 py-0.5 rounded-full">Required</span>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Travel and Tours operators (min: 1)</p>
                    </div>
                    <div class="bg-amber-100 p-2 rounded-full">
                        <i class="fas fa-ship text-amber-600"></i>
                    </div>
                </div>
                <div class="mt-3 flex items-end">
                    <span class="text-2xl font-bold text-amber-700"><?= $actual_crewCount; ?></span>
                    <span class="text-sm text-gray-500 ml-1 mb-1">/ 4</span>
                    <div class="ml-auto">
                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                            <div class="bg-amber-600 h-2.5 rounded-full" style="width: <?= ($actual_crewCount / 4) * 100 ?>%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Add Passenger Button -->
        <div class="mt-6 flex justify-between items-center">
            <h3 class="text-md font-semibold text-gray-700">Passenger List</h3>
            <button
                data-modal-target="addPassenger-modal"
                data-modal-toggle="addPassenger-modal"
                type="button"
                class="inline-flex items-center justify-center space-x-2 px-5 py-2.5 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-300 shadow-sm hover:shadow-md transition-all duration-300">
                <i class="fas fa-user-plus mr-2"></i>
                <span>Add Passenger</span>
            </button>


            <!-- Main modal -->
            <div id="addPassenger-modal" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                <div class="relative p-4 w-full max-w-7xl max-h-full">
                    <!-- Modal content -->
                    <div class="relative bg-white rounded-lg shadow-lg">
                        <!-- Modal header -->
                        <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t border-gray-200 bg-gradient-to-r from-blue-600 to-blue-700">
                            <div class="flex items-center">
                                <i class="fas fa-user-plus text-white mr-2"></i>
                                <h3 class="text-xl font-semibold text-white">
                                    Add New Passenger
                                </h3>
                            </div>
                            <button type="button" class="text-white bg-blue-800 hover:bg-blue-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center" data-modal-hide="addPassenger-modal">
                                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                </svg>
                                <span class="sr-only">Close modal</span>
                            </button>
                        </div>
                        <form action="inc/inc.passenger.php" method="POST">
                            <div class="p-4 md:p-5 space-y-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- Passenger Information Section -->
                                    <fieldset class="bg-gray-50 p-5 rounded-lg shadow-sm border border-gray-200">
                                        <legend class="px-2 text-lg font-bold text-blue-700 flex items-center">
                                            <i class="fas fa-id-card text-blue-500 mr-2"></i>
                                            Passenger Information
                                        </legend>

                                        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                                        <input type="hidden" value="<?= $_GET['id']; ?>" name="bookingId">

                                        <!-- Type of Passenger -->
                                        <div class="mb-4">
                                            <label class="block mb-2 text-sm font-medium text-gray-700">
                                                Type of Passenger <span class="text-red-600">*</span>
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                    <i class="fas fa-users text-blue-500"></i>
                                                </div>
                                                <select name="passengerType" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5" required>
                                                    <option value="" disabled selected>Select passenger type...</option>
                                                    <option value="crewTts">Crew</option>
                                                    <option value="tourist">Tourist</option>
                                                </select>
                                            </div>
                                            <p class="mt-1 text-xs text-gray-500">Select whether this person is a crew member or tourist</p>
                                        </div>

                                        <!-- Full Name -->
                                        <div class="mb-4">
                                            <label class="block mb-2 text-sm font-medium text-gray-700">
                                                Full Name <span class="text-red-600">*</span>
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                    <i class="fas fa-user text-blue-500"></i>
                                                </div>
                                                <input type="text" name="fullName" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5" placeholder="Enter full name" required>
                                            </div>
                                        </div>

                                        <!-- Citizenship -->
                                        <div class="mb-4">
                                            <label class="block mb-2 text-sm font-medium text-gray-700">
                                                Citizenship <span class="text-red-600">*</span>
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                    <i class="fas fa-flag text-blue-500"></i>
                                                </div>
                                                <select name="citizenship" id="citizenship" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5" required>
                                                    <option value="" disabled selected>Select citizenship...</option>
                                                    <option value="filipino">Filipino</option>
                                                    <option value="foreigner">Foreigner</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- Conditional Dropdown: Region if Filipino -->
                                        <div id="filipinoDropdown" class="hidden mb-4">
                                            <label class="block mb-2 text-sm font-medium text-gray-700">
                                                Region <span class="text-red-600">*</span>
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                    <i class="fas fa-map-marker-alt text-blue-500"></i>
                                                </div>
                                                <select name="region-country" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5">
                                                    <option value="" disabled selected>Select region...</option>
                                                    <option value="Camarines Norte">Camarines Norte</option>
                                                    <option value="Region-V">Region-V</option>
                                                    <option value="Philippines">Philippines</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- Conditional Dropdown: Country if Foreigner -->
                                        <div id="foreignerDropdown" class="hidden mb-4">
                                            <label class="block mb-2 text-sm font-medium text-gray-700">
                                                Country <span class="text-red-600">*</span>
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                    <i class="fas fa-globe text-blue-500"></i>
                                                </div>
                                                <select name="region-country" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5">
                                                    <option value="" disabled selected>Select country...</option>
                                                    <option value="North America">North America</option>
                                                    <option value="South America">South America</option>
                                                    <option value="United Kingdom">United Kingdom</option>
                                                    <option value="Asia">Asia</option>
                                                    <option value="Africa">Africa</option>
                                                    <option value="Australia">Australia</option>
                                                    <option value="Europe">Europe</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- Common Address Fields -->
                                        <div class="mb-4">
                                            <label class="block mb-2 text-sm font-medium text-gray-700">
                                                Address <span class="text-red-600">*</span>
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                    <i class="fas fa-home text-blue-500"></i>
                                                </div>
                                                <input type="text" name="address" list="addressPassenger" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5" placeholder="Enter complete address" required>
                                                <datalist id="addressPassenger">
                                                    <?php
                                                    foreach ($disAddressPassenger as $showAddressPassenger) {
                                                    ?>
                                                        <option value="<?php echo $showAddressPassenger['address']; ?>"><?php echo $showAddressPassenger['address']; ?></option>
                                                    <?php } ?>
                                                </datalist>
                                            </div>
                                            <p class="mt-1 text-xs text-gray-500">Previously used addresses will appear as suggestions</p>
                                        </div>
                                    </fieldset>

                                    <!-- Additional Details Section -->
                                    <fieldset class="bg-gray-50 p-5 rounded-lg shadow-sm border border-gray-200">
                                        <legend class="px-2 text-lg font-bold text-blue-700 flex items-center">
                                            <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                                            Additional Details
                                        </legend>

                                        <!-- Gender -->
                                        <div class="mb-4">
                                            <label class="block mb-2 text-sm font-medium text-gray-700">
                                                Gender <span class="text-red-600">*</span>
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                    <i class="fas fa-venus-mars text-blue-500"></i>
                                                </div>
                                                <select name="gender" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5" required>
                                                    <option value="" disabled selected>Select gender...</option>
                                                    <option value="Male">Male</option>
                                                    <option value="Female">Female</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- Age -->
                                        <div class="mb-4">
                                            <label class="block mb-2 text-sm font-medium text-gray-700">
                                                Age <span class="text-red-600">*</span>
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                    <i class="fas fa-birthday-cake text-blue-500"></i>
                                                </div>
                                                <input type="number" name="age" min="1" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5" placeholder="Enter age" required>
                                            </div>
                                            <p class="mt-1 text-xs text-gray-500">For children (8 and below), special rates may apply</p>
                                        </div>

                                        <!-- Contact Number -->
                                        <div class="mb-4">
                                            <label class="block mb-2 text-sm font-medium text-gray-700">
                                                Contact Number <span class="text-red-600">*</span>
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                    <i class="fas fa-phone text-blue-500"></i>
                                                </div>
                                                <input type="text" name="contactNumber" list="contactNumberPassenger" maxlength="11" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5" placeholder="Enter contact number" required>
                                                <datalist id="contactNumberPassenger">
                                                    <?php
                                                    foreach ($disContactPassenger as $showContactPassenger) {
                                                    ?>
                                                        <option value="<?php echo $showContactPassenger['contact_number']; ?>"><?php echo $showContactPassenger['contact_number']; ?></option>
                                                    <?php } ?>
                                                </datalist>
                                            </div>
                                            <p class="mt-1 text-xs text-gray-500">Previously used contact numbers will appear as suggestions</p>
                                        </div>

                                        <!-- Information Notice -->
                                        <div class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                                            <div class="flex">
                                                <div class="flex-shrink-0">
                                                    <i class="fas fa-info-circle text-blue-500"></i>
                                                </div>
                                                <div class="ml-3">
                                                    <h3 class="text-sm font-medium text-blue-800">Important Information</h3>
                                                    <div class="mt-2 text-sm text-blue-700">
                                                        <ul class="list-disc pl-5 space-y-1">
                                                            <li>All fields marked with <span class="text-red-600">*</span> are required</li>
                                                            <li>Ensure contact information is accurate for emergency purposes</li>
                                                            <li>For crew members, additional documentation may be required</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </fieldset>
                                </div>
                            </div>

                            <!-- Form Buttons -->
                            <div class="flex justify-end items-center p-4 md:p-5 border-t border-gray-200 rounded-b bg-gray-50">
                                <button type="button" data-modal-hide="addPassenger-modal" class="py-2.5 px-5 mr-3 text-sm font-medium text-gray-900 bg-white rounded-lg border border-gray-300 hover:bg-gray-100 hover:text-blue-600 focus:outline-none focus:ring-4 focus:ring-gray-200 shadow-sm">
                                    <i class="fas fa-times mr-1"></i> Cancel
                                </button>
                                <button type="submit" name="registerPassenger" class="text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center shadow-sm hover:shadow-md transition-all duration-300">
                                    <i class="fas fa-save mr-1"></i> Register Passenger
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Table Passenger Info -->
        <div class="overflow-x-auto mt-4">
            <?php
            // Fetch rows
            try {
                $sql = "
        SELECT *
        FROM cb_tourists
        WHERE booking_id = :booking_id
        ORDER BY info_type = 'crewTts' DESC, full_name ASC
    ";
                $stmt = $pdo->prepare($sql);
                $stmt->bindParam(':booking_id', $getBookingId, PDO::PARAM_STR);
                $stmt->execute();
                $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
            } catch (PDOException $e) {
                echo '<p class="px-6 py-4 text-center text-sm text-red-600">'
                    . 'Error: ' . htmlspecialchars($e->getMessage()) .
                    '</p>';
                $rows = [];
            }
            ?>

            <?php if (!empty($rows)): ?>

                <!-- Desktop Table (md and up) -->
                <div class="hidden md:block overflow-x-auto mt-4">
                    <table id="search-table" class="min-w-full bg-white border border-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Age</th>
                                <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            <?php foreach ($rows as $row):
                                $infoTypeBadge = $row['info_type'] === 'tourist'
                                    ? '<span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-sm">Tourist</span>'
                                    : '<span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-sm">Crew</span>';
                            ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                        <?= $infoTypeBadge; ?>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-500 text-center">
                                        <?= htmlspecialchars($row['full_name']); ?>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-500 text-center">
                                        <?= htmlspecialchars($row['age']); ?>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-center">
                                        <div class="inline-flex space-x-2">
                                            <!-- Delete -->
                                            <button
                                                data-modal-target="delete-passenger-modal-<?= $row['tourist_id']; ?>"
                                                data-modal-toggle="delete-passenger-modal-<?= $row['tourist_id']; ?>"
                                                class="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-red-500">
                                                Delete
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Mobile Cards (smaller than md) -->
                <div class="md:hidden space-y-4 mt-4">
                    <?php foreach ($rows as $row):
                        $infoTypeBadge = $row['info_type'] === 'tourist'
                            ? '<span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-sm">Tourist</span>'
                            : '<span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-sm">Crew</span>';
                    ?>
                        <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                            <div class="flex justify-between items-center">
                                <?= $infoTypeBadge; ?>
                                <div class="text-sm font-medium"><?= htmlspecialchars($row['full_name']); ?></div>
                            </div>
                            <div class="mt-2 flex justify-between items-center">
                                <div class="text-sm text-gray-500"><?= htmlspecialchars($row['age']); ?> yrs</div>
                                <div class="inline-flex space-x-2">
                                    <button
                                        data-modal-target="delete-passenger-modal-<?= $row['tourist_id']; ?>"
                                        data-modal-toggle="delete-passenger-modal-<?= $row['tourist_id']; ?>"
                                        class="px-3 py-1.5 text-xs font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-red-500">
                                        Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

            <?php else: ?>
                <p class="mt-4 text-center text-gray-500">No passengers found.</p>
            <?php endif; ?>

            <!-- Delete Passenger Modals -->
            <?php if (!empty($rows)): ?>
                <?php foreach ($rows as $row): ?>
                    <!-- Delete Confirmation Modal -->
                    <div id="delete-passenger-modal-<?= $row['tourist_id']; ?>" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                        <div class="relative p-4 w-full max-w-md max-h-full">
                            <div class="relative bg-white rounded-lg shadow-lg border border-gray-200">
                                <!-- Modal header -->
                                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t border-gray-200 bg-gradient-to-r from-red-600 to-red-700">
                                    <div class="flex items-center">
                                        <i class="fas fa-trash-alt text-white mr-2"></i>
                                        <h3 class="text-xl font-semibold text-white">
                                            Delete Passenger
                                        </h3>
                                    </div>
                                    <button type="button" class="text-white bg-red-800 hover:bg-red-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center" data-modal-hide="delete-passenger-modal-<?= $row['tourist_id']; ?>">
                                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                        </svg>
                                        <span class="sr-only">Close modal</span>
                                    </button>
                                </div>

                                <form action="inc/inc.passenger.php" method="POST">
                                    <div class="p-4 md:p-5">
                                        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                                        <input type="hidden" name="passengerId" value="<?= $row['tourist_id']; ?>">
                                        <input type="hidden" name="bookingId" value="<?= $getBookingId; ?>">

                                        <!-- Warning Message -->
                                        <div class="flex p-4 mb-4 text-red-800 rounded-lg bg-red-50 border border-red-200" role="alert">
                                            <div class="flex-shrink-0">
                                                <i class="fas fa-exclamation-triangle text-red-600"></i>
                                            </div>
                                            <div class="ml-3 text-sm font-medium text-red-800">
                                                Are you sure you want to delete this passenger? This action cannot be undone.
                                            </div>
                                        </div>

                                        <!-- Passenger Info -->
                                        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-4">
                                            <div class="flex justify-between items-center mb-2">
                                                <span class="text-sm font-medium text-gray-600">Name:</span>
                                                <span class="text-sm font-bold text-gray-800"><?= htmlspecialchars($row['full_name']); ?></span>
                                            </div>
                                            <div class="flex justify-between items-center mb-2">
                                                <span class="text-sm font-medium text-gray-600">Type:</span>
                                                <span class="text-sm font-bold text-gray-800"><?= ucfirst($row['info_type'] === 'tourist' ? 'Tourist' : 'Crew'); ?></span>
                                            </div>
                                            <div class="flex justify-between items-center">
                                                <span class="text-sm font-medium text-gray-600">Age:</span>
                                                <span class="text-sm font-bold text-gray-800"><?= htmlspecialchars($row['age']); ?></span>
                                            </div>
                                        </div>

                                        <!-- Action Buttons -->
                                        <div class="flex justify-end space-x-3 mt-6">
                                            <button data-modal-hide="delete-passenger-modal-<?= $row['tourist_id']; ?>" type="button" class="py-2.5 px-5 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-300 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 shadow-sm">
                                                <i class="fas fa-times mr-1"></i> Cancel
                                            </button>
                                            <button type="submit" name="deletePassenger" class="text-white bg-red-600 hover:bg-red-700 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center shadow-sm hover:shadow-md transition-all duration-300">
                                                <i class="fas fa-trash-alt mr-1"></i> Delete Passenger
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>

        </div>
        <div class="flex flex-col md:flex-row justify-center md:justify-end mt-4 space-y-4 md:space-y-0 md:space-x-4">
            <?php
            // Check passenger requirements and update button status
            if ($paymentBtn === 'disabled') {
                $btnClass = "inline-flex items-center justify-center bg-gray-400 cursor-not-allowed text-white font-medium rounded-md text-sm p-3";

                // Create specific tooltip message based on what's missing
                if ($actual_adultCount < 1 && $actual_crewCount < 1) {
                    $tooltipAttr = 'title="Please add at least 1 adult and 1 crew member before proceeding"';
                } elseif ($actual_adultCount < 1) {
                    $tooltipAttr = 'title="Please add at least 1 adult before proceeding"';
                } elseif ($actual_crewCount < 1) {
                    $tooltipAttr = 'title="Please add at least 1 crew member before proceeding"';
                } else {
                    $tooltipAttr = 'title="Please add required passengers before proceeding"';
                }
            } else {
                $btnClass = "inline-flex items-center justify-center bg-green-600 hover:bg-green-700 text-white font-medium rounded-md text-sm p-3";
                $tooltipAttr = '';
            }
            ?>

            <?php if ($bookingDetails['booking_status'] == 'declined'): ?>
            <!-- Return to Edit Booking button for declined bookings -->
            <a href="booking-edit.php?id=<?= $getBookingId; ?>" class="inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md text-sm p-3">
                <i class="fas fa-arrow-left mr-2"></i> Return to Edit Booking
            </a>
            <?php else: ?>
            <!-- Only show Proceed button for draft bookings -->
            <button
                type="button"
                data-modal-target="proceed-confirmation-modal"
                data-modal-toggle="proceed-confirmation-modal"
                class="<?= $btnClass; ?>"
                <?= $paymentBtn; ?>
                <?= $tooltipAttr; ?>
            >
                <i class="fas fa-check-circle mr-2"></i> Proceed
            </button>
            <?php endif; ?>
        </div>

        <!-- Proceed Confirmation Modal -->
        <div id="proceed-confirmation-modal" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
            <div class="relative p-4 w-full max-w-md max-h-full">
                <div class="relative bg-white rounded-lg shadow-lg border border-gray-200">
                    <!-- Modal header -->
                    <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t border-gray-200 bg-gradient-to-r from-green-600 to-green-700">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-white mr-2"></i>
                            <h3 class="text-xl font-semibold text-white">
                                Confirm Booking
                            </h3>
                        </div>
                        <button type="button" class="text-white bg-green-800 hover:bg-green-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center" data-modal-hide="proceed-confirmation-modal">
                            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                            </svg>
                            <span class="sr-only">Close modal</span>
                        </button>
                    </div>

                    <form action="inc/inc.booking.php" method="POST">
                        <div class="p-4 md:p-5">
                            <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                            <input type="hidden" name="booking_id" value="<?= $getBookingId; ?>">
                            <input type="hidden" name="referenceNumber" value="<?= $referenceNumber; ?>">

                            <!-- Booking Summary -->
                            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-4">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium text-gray-600">Reference Number:</span>
                                    <span class="text-sm font-bold text-blue-600"><?= $referenceNumber; ?></span>
                                </div>
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium text-gray-600">Destination:</span>
                                    <span class="text-sm font-bold text-gray-800"><?= htmlspecialchars($resortName, ENT_QUOTES, 'UTF-8'); ?></span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm font-medium text-gray-600">Total Passengers:</span>
                                    <span class="text-sm font-bold text-gray-800"><?= $actual_adultCount + $actual_childrenCount + $actual_crewCount; ?> people</span>
                                </div>
                            </div>

                            <!-- Warning Message -->
                            <div class="flex p-4 mb-4 text-blue-800 rounded-lg bg-blue-50 border border-blue-200" role="alert">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-info-circle text-blue-600"></i>
                                </div>
                                <div class="ml-3 text-sm font-medium text-blue-800">
                                    By proceeding, your booking will be submitted for approval. You will need to wait for confirmation from the resort and boat operators before you can proceed to payment.
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex justify-end space-x-3 mt-6">
                                <button data-modal-hide="proceed-confirmation-modal" type="button" class="py-2.5 px-5 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-300 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 shadow-sm">
                                    <i class="fas fa-times mr-1"></i> Cancel
                                </button>
                                <button type="submit" name="proceedBtn" class="text-white bg-green-600 hover:bg-green-700 focus:ring-4 focus:outline-none focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center shadow-sm hover:shadow-md transition-all duration-300">
                                    <i class="fas fa-check mr-1"></i> Confirm Booking
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        </div>
    </div>
</div>
<!-- JavaScript for Page Functionality -->
<script>
    // Toggle Conditional Dropdowns
    document.getElementById('citizenship').addEventListener('change', function() {
        var selected = this.value;
        var filipinoDropdown = document.getElementById('filipinoDropdown');
        var foreignerDropdown = document.getElementById('foreignerDropdown');

        if (selected === 'filipino') {
            filipinoDropdown.classList.remove('hidden');
            foreignerDropdown.classList.add('hidden');
        } else if (selected === 'foreigner') {
            foreignerDropdown.classList.remove('hidden');
            filipinoDropdown.classList.add('hidden');
        } else {
            filipinoDropdown.classList.add('hidden');
            foreignerDropdown.classList.add('hidden');
        }
    });

    // Alert Dismissal
    document.addEventListener('DOMContentLoaded', function() {
        // Get all dismiss buttons
        const dismissButtons = document.querySelectorAll('[data-dismiss-target]');

        // Add click event to each button
        dismissButtons.forEach(button => {
            button.addEventListener('click', function() {
                const targetSelector = this.getAttribute('data-dismiss-target');
                const targetElement = this.closest(targetSelector);

                if (targetElement) {
                    // Add fade-out animation
                    targetElement.classList.add('opacity-0', 'transition-opacity', 'duration-300');

                    // Remove the element after animation completes
                    setTimeout(() => {
                        targetElement.remove();
                    }, 300);
                }
            });
        });
    });
</script>


<?php
require '_footer.php';
?>