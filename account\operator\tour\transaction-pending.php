<?php
require '_header.php';
?>

<div class="p-4 sm:ml-64">
    <div class="mt-16"> <!-- Adjusted for top navbar -->
        <!-- Main Content Section -->
        <div class="border p-4 rounded-lg shadow-lg bg-white mb-6">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                <li class="inline-flex items-center">
                    <a href="home.php" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-gray-900">
                        <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                        </svg>
                        Home
                    </a>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2">Transactions</span>
                    </div>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2">Pending</span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- Header Section with Icon -->
        <div class="flex items-center mt-6">
            <div class="bg-yellow-100 p-3 rounded-lg mr-4">
                <i class="fas fa-clock text-yellow-600 text-xl"></i>
            </div>
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Pending Bookings</h2>
                <p class="text-sm text-gray-600 mt-1">Bookings awaiting approval from operators</p>
            </div>
        </div>

        <!-- Table -->
        <div class="mt-6 overflow-hidden">
            <div class="overflow-x-auto rounded-lg">
                <table id="search-table" class="min-w-full bg-white border border-gray-200 rounded-lg shadow-sm table-responsive">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reference</th>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Resort</th>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Boat</th>
                            <th class="px-4 sm:px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                        </tr>
                    </thead>
                <tbody class="divide-y divide-gray-200">
                    <?php
                    try {
                        $booking_status = "pending";

                        $bookingDetails = getAllBookingDetails($pdo, $booking_status);

                        if ($bookingDetails && count($bookingDetails) > 0) {
                            foreach ($bookingDetails as $row) {
                                $setColorResort = ($row['resort'] === "Approved") ? "green" : "yellow";
                                $setColorBoat   = ($row['boat'] === "Approved")   ? "green" : "yellow";
                                $setColorTreasurer = ($row['treasurer'] === "Approved") ? "green" : "yellow";
                                $setColorMtho = ($row['mtho'] === "Approved") ? "green" : "yellow";

                                // Check if both resort and boat have approved
                                if($row['mtho'] === "Waiting"){
                                    $bothApproved = ($row['resort'] === "Approved" && $row['boat'] === "Approved");
                                }else{
                                    $bothApproved = "";
                                }
                    ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm font-medium text-blue-600"><?= htmlspecialchars($row['referenceNum'] ?? 'N/A'); ?></td>
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm font-medium text-gray-900"><?= htmlspecialchars($row['designation']); ?></td>
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-gray-700"><?= htmlspecialchars($row['boatName']); ?></td>
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-center">
                                        <div class="flex flex-col sm:flex-row justify-center sm:space-x-2 space-y-2 sm:space-y-0">
                                            <button
                                                data-modal-target="view-approvals-modal-<?= $row['booking_id']; ?>"
                                                data-modal-toggle="view-approvals-modal-<?= $row['booking_id']; ?>"
                                                type="button"
                                                class="inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md text-xs px-2 sm:px-3 py-1.5 w-full sm:w-auto"
                                            >
                                                <i class="fas fa-clipboard-check mr-1"></i> <span class="whitespace-nowrap">View Details</span>
                                            </button>

                                            <?php if($bothApproved): ?>
                                            <a
                                                href="payment-tdf.php?id=<?= $row['booking_id']; ?>"
                                                class="inline-flex items-center justify-center bg-green-600 hover:bg-green-700 text-white font-medium rounded-md text-xs px-2 sm:px-3 py-1.5 w-full sm:w-auto"
                                            >
                                                <i class="fas fa-credit-card mr-1"></i> <span class="whitespace-nowrap">Payment</span>
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <!-- View Passenger Modal -->
                                <div id="view-approvals-modal-<?= $row['booking_id']; ?>" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                    <div class="relative w-full max-w-md max-h-full"> <!-- Increased max-width to max-w-md for better readability -->
                                        <!-- Modal Container -->
                                        <div class="relative bg-white rounded-lg shadow-lg">
                                            <!-- Modal Header -->
                                            <div class="flex items-center justify-between p-4 bg-gradient-to-r from-blue-600 to-blue-700 rounded-t-lg">
                                                <div class="flex items-center">
                                                    <i class="fas fa-clipboard-check text-white mr-2"></i>
                                                    <h3 class="text-xl font-semibold text-white">
                                                        Approval Status
                                                    </h3>
                                                </div>
                                                <button type="button" class="text-white hover:text-gray-200 rounded-full w-8 h-8 flex justify-center items-center transition-colors duration-200 bg-blue-800 hover:bg-blue-900" data-modal-hide="view-approvals-modal-<?= $row['booking_id']; ?>">
                                                    <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                                    </svg>
                                                    <span class="sr-only">Close modal</span>
                                                </button>
                                            </div>

                                            <!-- Modal Body -->
                                            <div class="p-5 bg-white rounded-lg space-y-5">
                                                <!-- Booking Reference -->
                                                <div class="bg-blue-50 p-3 rounded-lg border border-blue-100 mb-4">
                                                    <p class="text-xs text-blue-500 uppercase font-medium">Reference Number</p>
                                                    <p class="text-sm font-semibold text-blue-800 mt-1"><?= htmlspecialchars($row['referenceNum'] ?? 'N/A'); ?></p>
                                                </div>

                                                <!-- Status Cards -->
                                                <div class="grid grid-cols-1 gap-3">
                                                    <!-- Resort -->
                                                    <div class="bg-white border border-gray-200 rounded-lg p-3 shadow-sm flex items-center justify-between">
                                                        <div class="flex items-center">
                                                            <div class="bg-blue-100 p-2 rounded-full mr-3">
                                                                <i class="fas fa-hotel text-blue-600"></i>
                                                            </div>
                                                            <span class="text-sm font-medium text-gray-700">Resort</span>
                                                        </div>
                                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                                            <?php if($row['resort'] === 'Approved'): ?>
                                                                bg-green-100 text-green-800
                                                            <?php elseif($row['resort'] === 'Pending'): ?>
                                                                bg-yellow-100 text-yellow-800
                                                            <?php else: ?>
                                                                bg-gray-100 text-gray-800
                                                            <?php endif; ?>">
                                                            <?php if($row['resort'] === 'Approved'): ?>
                                                                <i class="fas fa-check-circle mr-1"></i>
                                                            <?php elseif($row['resort'] === 'Pending'): ?>
                                                                <i class="fas fa-clock mr-1"></i>
                                                            <?php else: ?>
                                                                <i class="fas fa-hourglass-half mr-1"></i>
                                                            <?php endif; ?>
                                                            <?= $row['resort']; ?>
                                                        </span>
                                                    </div>

                                                    <!-- Boat -->
                                                    <div class="bg-white border border-gray-200 rounded-lg p-3 shadow-sm flex items-center justify-between">
                                                        <div class="flex items-center">
                                                            <div class="bg-blue-100 p-2 rounded-full mr-3">
                                                                <i class="fas fa-ship text-blue-600"></i>
                                                            </div>
                                                            <span class="text-sm font-medium text-gray-700">Boat</span>
                                                        </div>
                                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                                            <?php if($row['boat'] === 'Approved'): ?>
                                                                bg-green-100 text-green-800
                                                            <?php elseif($row['boat'] === 'Pending'): ?>
                                                                bg-yellow-100 text-yellow-800
                                                            <?php else: ?>
                                                                bg-gray-100 text-gray-800
                                                            <?php endif; ?>">
                                                            <?php if($row['boat'] === 'Approved'): ?>
                                                                <i class="fas fa-check-circle mr-1"></i>
                                                            <?php elseif($row['boat'] === 'Pending'): ?>
                                                                <i class="fas fa-clock mr-1"></i>
                                                            <?php else: ?>
                                                                <i class="fas fa-hourglass-half mr-1"></i>
                                                            <?php endif; ?>
                                                            <?= $row['boat']; ?>
                                                        </span>
                                                    </div>

                                                    <!-- Treasurer -->
                                                    <div class="bg-white border border-gray-200 rounded-lg p-3 shadow-sm flex items-center justify-between">
                                                        <div class="flex items-center">
                                                            <div class="bg-blue-100 p-2 rounded-full mr-3">
                                                                <i class="fas fa-money-bill-wave text-blue-600"></i>
                                                            </div>
                                                            <span class="text-sm font-medium text-gray-700">Treasurer</span>
                                                        </div>
                                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                                            <?php if($row['treasurer'] === 'Approved'): ?>
                                                                bg-green-100 text-green-800
                                                            <?php elseif($row['treasurer'] === 'Pending'): ?>
                                                                bg-yellow-100 text-yellow-800
                                                            <?php else: ?>
                                                                bg-gray-100 text-gray-800
                                                            <?php endif; ?>">
                                                            <?php if($row['treasurer'] === 'Approved'): ?>
                                                                <i class="fas fa-check-circle mr-1"></i>
                                                            <?php elseif($row['treasurer'] === 'Pending'): ?>
                                                                <i class="fas fa-clock mr-1"></i>
                                                            <?php else: ?>
                                                                <i class="fas fa-hourglass-half mr-1"></i>
                                                            <?php endif; ?>
                                                            <?= $row['treasurer']; ?>
                                                        </span>
                                                    </div>

                                                    <!-- MTHO -->
                                                    <div class="bg-white border border-gray-200 rounded-lg p-3 shadow-sm flex items-center justify-between">
                                                        <div class="flex items-center">
                                                            <div class="bg-blue-100 p-2 rounded-full mr-3">
                                                                <i class="fas fa-building text-blue-600"></i>
                                                            </div>
                                                            <span class="text-sm font-medium text-gray-700">MTHO</span>
                                                        </div>
                                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                                            <?php if($row['mtho'] === 'Approved'): ?>
                                                                bg-green-100 text-green-800
                                                            <?php elseif($row['mtho'] === 'Pending'): ?>
                                                                bg-yellow-100 text-yellow-800
                                                            <?php else: ?>
                                                                bg-gray-100 text-gray-800
                                                            <?php endif; ?>">
                                                            <?php if($row['mtho'] === 'Approved'): ?>
                                                                <i class="fas fa-check-circle mr-1"></i>
                                                            <?php elseif($row['mtho'] === 'Pending'): ?>
                                                                <i class="fas fa-clock mr-1"></i>
                                                            <?php else: ?>
                                                                <i class="fas fa-hourglass-half mr-1"></i>
                                                            <?php endif; ?>
                                                            <?= $row['mtho']; ?>
                                                        </span>
                                                    </div>
                                                </div>

                                                <?php if($row['resort'] === 'Approved' && $row['boat'] === 'Approved'): ?>
                                                <!-- Approval Complete Message -->
                                                <div class="flex p-4 mt-2 text-green-800 rounded-lg bg-green-50 border border-green-200" role="alert">
                                                    <div class="flex-shrink-0">
                                                        <i class="fas fa-check-circle text-green-600"></i>
                                                    </div>
                                                    <div class="ml-3 text-sm font-medium text-green-800">
                                                        Both Resort and Boat operators have approved this booking. You can now proceed to payment.
                                                    </div>
                                                </div>
                                                <?php else: ?>
                                                <!-- Waiting for Approval Message -->
                                                <div class="flex p-4 mt-2 text-yellow-800 rounded-lg bg-yellow-50 border border-yellow-200" role="alert">
                                                    <div class="flex-shrink-0">
                                                        <i class="fas fa-exclamation-circle text-yellow-600"></i>
                                                    </div>
                                                    <div class="ml-3 text-sm font-medium text-yellow-800">
                                                        Waiting for approval from all required operators before proceeding to payment.
                                                    </div>
                                                </div>
                                                <?php endif; ?>
                                            </div>

                                            <!-- Modal Footer -->
                                            <div class="flex justify-end items-center p-4 bg-gray-50 border-t border-gray-100 rounded-b-lg">
                                                <?php if($row['resort'] === 'Approved' && $row['boat'] === 'Approved'): ?>
                                                <a href="payment-tdf.php?id=<?= $row['booking_id']; ?>" class="py-2 px-4 mr-3 text-sm font-semibold text-white bg-green-600 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all duration-200">
                                                    <i class="fas fa-credit-card mr-1"></i> Proceed to Payment
                                                </a>
                                                <?php endif; ?>
                                                <button data-modal-hide="view-approvals-modal-<?= $row['booking_id']; ?>" type="button" class="py-2 px-4 text-sm font-semibold text-white bg-red-500 rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-400 transition-all duration-200">
                                                    <i class="fas fa-times mr-1"></i> Close
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        <?php
                            }
                        } else {
                            // No pending bookings found
                            ?>
                            <tr>
                                <td colspan="6" class="px-4 sm:px-6 py-6 sm:py-8 text-center">
                                    <div class="flex flex-col items-center justify-center">
                                        <div class="bg-yellow-100 p-3 rounded-full mb-3">
                                            <i class="fas fa-inbox text-yellow-500 text-xl"></i>
                                        </div>
                                        <p class="text-gray-500 text-sm font-medium">No pending bookings found</p>
                                        <p class="text-gray-400 text-xs mt-1 max-w-xs mx-auto">When bookings are pending approval, they will appear here</p>
                                    </div>
                                </td>
                            </tr>
                            <?php
                        }
                    } catch (PDOException $e) {
                        ?>
                        <tr>
                            <td colspan="6" class="px-4 sm:px-6 py-4 text-center">
                                <div class="bg-red-50 border border-red-200 rounded-lg p-3 inline-block mx-auto">
                                    <p class="text-sm text-red-600 font-medium">
                                        <i class="fas fa-exclamation-circle mr-1"></i> Error: <?= htmlspecialchars($e->getMessage()); ?>
                                    </p>
                                </div>
                            </td>
                        </tr>
                    <?php
                    }
                    ?>
                </tbody>
            </table>
        </div>
        </div>
    </div>


<?php
require '_footer.php';
?>