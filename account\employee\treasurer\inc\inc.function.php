<?php

function getBookingDetails($pdo, $getTreasurerStatus)
{
    try {
        $sql = "SELECT bb.booking_id AS booking_id,
                       bb.referenceNum,
                       oi.designation AS tour_operator_designation, 
                       oi.firstname, 
                       oi.middlename, 
                       oi.lastname, 
                       oi.extname, 
                       cp.total_adults,
                       cp.total_children,
                       cp.port_fee,
                       cp.voucher_use,
                       cp.total_amount,
                       cp.or_num,
                       cp.payment_method,
                       cp.payment_status
                FROM cb_bookings bb
                JOIN operator_info oi ON bb.tour_operator_id = oi.user_id
                JOIN cb_payments cp ON bb.booking_id = cp.booking_id
                JOIN cb_booking_approvals cba ON bb.booking_id = cba.booking_id
                WHERE cba.treasurer = :treasurer";

        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':treasurer', $getTreasurerStatus, PDO::PARAM_INT);
        $stmt->execute();

        // Fetch all rows
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return $rows ?: []; // Return empty array if no rows found
    } catch (PDOException $e) {
        error_log("Database error: " . $e->getMessage());
        return [];
    }
}

function getNotification(PDO $pdo, $treasurerStatus): array
{
    $stmt = $pdo->prepare("
        SELECT 
            SUM(treasurer = 'Pending') AS pending_count
        FROM cb_booking_approvals
        WHERE treasurer = :treasurer
    ");
    $stmt->bindParam(':treasurer', $treasurerStatus, PDO::PARAM_STR);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    return [
        'pending' => (int) ($result['pending_count'] ?? 0)
    ];
}
