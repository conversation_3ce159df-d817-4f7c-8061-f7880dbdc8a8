<?php
session_start([
    'cookie_secure'   => true,       // Only send cookie over HTTPS
    'cookie_httponly' => true,       // <PERSON><PERSON> cannot access the cookie
    'cookie_samesite' => 'Strict'    // Prevent CSRF in modern browsers
]);
include '../../../../connection/dbconnect.php';
if ($_SESSION['loginStatus'] != "isLogin") {
    header("Location: ../logout.php");
    exit;
} else {

    if ($_SERVER["REQUEST_METHOD"] == "POST") {
        if (isset($_POST['treasurerResponseBtn'])) {
            try {

                if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
                    throw new Exception("Invalid CSRF token.");
                }
                if (!isset($_SESSION['id'])) {
                    throw new Exception("User not authenticated");
                }

                // Validate Booking ID
                $getBookingId = $_POST['booking_id'] ?? null;
                if (!$getBookingId) {
                    throw new Exception("Invalid booking ID.");
                }
                $treasurerResponse = trim($_POST['treasurerResponse']);
                $referenceNumber = trim($_POST['referenceNumber']);
                $orNumber = $_POST['orNumber'];
                $paymentStatus = "paid";

                // Start transaction
                $pdo->beginTransaction();

                // Update Approvals
                $stmt = $pdo->prepare("UPDATE cb_booking_approvals SET treasurer = :treasurerResponse WHERE booking_id = :booking_id");
                $stmt->execute([
                    ':treasurerResponse' => $treasurerResponse,
                    ':booking_id' => $getBookingId
                ]);

                // Update Payment Or
                $stmt = $pdo->prepare("UPDATE cb_payments SET or_num = :orNumber, payment_status=:paymentStatus WHERE booking_id = :booking_id");
                $stmt->execute([
                    ':paymentStatus' => $paymentStatus,
                    ':orNumber' => $orNumber,
                    ':booking_id' => $getBookingId
                ]);

                // Insert log entry
                $type = "Booking - " . $treasurerResponse;
                $description = "Treasurer: " . $_SESSION['username'] . " " . $treasurerResponse . " a booking: " . $referenceNumber  . ". Date Created: " . date("Y-m-d");
                $stmt = $pdo->prepare("INSERT INTO system_logs (type, description) VALUES (:type, :description)");
                $stmt->execute([
                    ':type' => $type,
                    ':description' => $description
                ]);


                // Commit transaction
                $pdo->commit();

                $_SESSION['success'] = $treasurerResponse . " successfully";
                header("Location: ../transaction-approved.php");
                exit();
            } catch (Exception $e) {
                $pdo->rollBack();
                $_SESSION['error'] = $e->getMessage();
                header("Location: ../transaction-pending.php");
                exit();
            }
        }
    }
}
