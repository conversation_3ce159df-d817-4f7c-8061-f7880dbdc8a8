<?php
// Start the session with secure cookie parameters
session_start([
    'cookie_secure'   => true,       // Only send cookie over HTTPS
    'cookie_httponly' => true,       // J<PERSON> cannot access the cookie
    'cookie_samesite' => 'Strict'    // Prevent CSRF in modern browsers
]);

include 'connection/dbconnect.php';
include 'config/email.php';

// Basic rate limiting: track reset attempts in the session
if (!isset($_SESSION['reset_attempts'])) {
    $_SESSION['reset_attempts'] = 0;
    $_SESSION['reset_attempts_time'] = time();
}

// Reset counter if more than 1 hour has passed
if (time() - $_SESSION['reset_attempts_time'] > 3600) {
    $_SESSION['reset_attempts'] = 0;
    $_SESSION['reset_attempts_time'] = time();
}

$maxAttempts = 3;  // Maximum allowed attempts per hour

if ($_SESSION['reset_attempts'] >= $maxAttempts) {
    $_SESSION['error'] = "Too many password reset attempts. Please try again later.";
    header("Location: forgotpassword.php");
    exit();
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Clear any previous errors
    unset($_SESSION['error']);

    $email = trim($_POST['email'] ?? '');

    try {
        // Ensure CSRF tokens exist and compare them in constant time
        if (
            empty($_SESSION['csrf_token']) || empty($_POST['csrf_token']) ||
            !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])
        ) {
            throw new Exception("Invalid request. Please try again.");
        }

        // Validate email format
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception("Please enter a valid email address.");
        }

        // Check if email exists in the database
        $stmt = $pdo->prepare("SELECT id, username, email FROM operator_account WHERE email = :email AND accountStatus = 'Activated'");
        $stmt->execute([':email' => $email]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        // Always show success message for security (don't reveal if email exists)
        // But only send email if user actually exists
        if ($user) {
            // Generate a secure reset token
            $resetToken = bin2hex(random_bytes(32));
            $tokenExpiry = date('Y-m-d H:i:s', time() + 3600); // 1 hour expiry

            // Store the reset token in the database
            $stmt = $pdo->prepare("UPDATE operator_account SET rememberToken = :token, accountExpired = :expiry WHERE id = :id");
            $stmt->execute([
                ':token' => hash('sha256', $resetToken),
                ':expiry' => $tokenExpiry,
                ':id' => $user['id']
            ]);

            // Create reset link
            $resetLink = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) . "/resetpassword.php?token=" . urlencode($resetToken);

            // Send email using the email configuration
            $emailSent = sendPasswordResetEmail($user['email'], $user['username'], $resetLink);

            // Log the password reset attempt
            $stmt = $pdo->prepare("INSERT INTO system_logs (type, description) VALUES (:type, :description)");
            $stmt->execute([
                ':type' => 'Reset Request',
                ':description' => 'Password reset for: ' . $email . ' (ID: ' . $user['id'] . ')'
            ]);

            // Log email sending attempt
            logEmailAttempt('Password Reset', $user['email'], $emailSent, $emailSent ? '' : 'Mail function failed');

            if (!$emailSent) {
                error_log("Failed to send password reset email to: " . $email);
                // Don't reveal email sending failure to user for security
            }
        }

        // Always show success message regardless of whether email exists
        $_SESSION['success'] = "If an account with that email address exists, we have sent you a password reset link.";

        // Increment reset attempts
        $_SESSION['reset_attempts']++;

        header("Location: forgotpassword.php");
        exit();

    } catch (Exception $e) {
        $_SESSION['reset_attempts']++;
        $_SESSION['error'] = $e->getMessage();
        header("Location: forgotpassword.php");
        exit();
    }
} else {
    // Redirect if not POST request
    header("Location: forgotpassword.php");
    exit();
}
?>
