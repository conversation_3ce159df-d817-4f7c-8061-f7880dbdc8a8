<?php
session_start([
    'cookie_secure'   => true,       // Only send cookie over HTTPS
    'cookie_httponly' => true,       // <PERSON><PERSON> cannot access the cookie
    'cookie_samesite' => 'Strict'    // Prevent CSRF in modern browsers
]);
include '../../../../connection/dbconnect.php';
include 'inc.function.php';
if ($_SESSION['loginStatus'] != "isLogin") {
    header("Location: ../logout.php");
    exit;
} else {

    if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['registerPassenger'])) {
        try {
            // CSRF Protection
            if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
                throw new Exception("Invalid CSRF token.");
            }

            // Sanitize and Validate Input
            $bookingId = filter_input(INPUT_POST, 'bookingId', FILTER_SANITIZE_NUMBER_INT);
            $passengerType = htmlspecialchars(trim($_POST['passengerType']), ENT_QUOTES, 'UTF-8');
            $fullName = strtoupper(htmlspecialchars(trim($_POST['fullName']), ENT_QUOTES, 'UTF-8'));
            $regCount = htmlspecialchars(trim($_POST['region-country']), ENT_QUOTES, 'UTF-8');
            $address = strtoupper(htmlspecialchars(trim($_POST['address']), ENT_QUOTES, 'UTF-8'));
            $gender = htmlspecialchars(trim($_POST['gender']), ENT_QUOTES, 'UTF-8');
            $age = filter_var($_POST['age'], FILTER_VALIDATE_INT);
            $contactNumber = htmlspecialchars(trim($_POST['contactNumber']), ENT_QUOTES, 'UTF-8');


            // Validate Required Fields
            if (empty($bookingId) || empty($passengerType) || empty($fullName) || empty($address) || empty($gender) || empty($age) || empty($contactNumber)) {
                throw new Exception("All fields are required.");
            }

            if (!$age || $age < 0) {
                throw new Exception("Invalid age provided.");
            }
            // For checking
            $bookingDetails = getBookingDetails2($pdo, $getBookingId);
            $counts = getCrewCount($pdo, $getBookingId);

            // Expectec Final Count of Passengers
            $crewCount = 4;

            // Ongoing Count of Passengers
            $actual_crewCount = $counts['crew'];


            if ($crewCount === $actual_crewCount) {
                throw new Exception("The number of crew exceeds the total number of children that are permitted");
            }

            // Start Transaction
            $pdo->beginTransaction();

            // Check if the tourist already exists
            $stmt = $pdo->prepare("SELECT 1 FROM cb_tourists WHERE info_type = :info_type AND LOWER(full_name) = LOWER(:full_name) LIMIT 1");
            $stmt->execute([
                ':info_type' => $passengerType,
                ':full_name' => $fullName
            ]);

            if ($stmt->fetch()) {
                throw new Exception("Tourist already exists.");
            }

            // Insert into cb_tourist
            $stmt = $pdo->prepare("INSERT INTO cb_tourists (booking_id, full_name, demographic, address, gender, age, contact_number, info_type) 
        VALUES (:bookingId, :fullName, :regCount, :address, :gender, :age, :contactNumber, :passengerType)");
            $stmt->execute([
                ':bookingId' => $bookingId,
                ':fullName' => $fullName,
                ':regCount' => $regCount,
                ':address' => $address,
                ':gender' => $gender,
                ':age' => $age,
                ':contactNumber' => $contactNumber,
                ':passengerType' => $passengerType
            ]);

            // Commit Transaction
            $pdo->commit();

            $_SESSION['success'] = "Passenger has been added successfully.";
            header("Location: ../add-passenger.php?id=" . htmlspecialchars($bookingId));
            exit();
        } catch (Exception $e) {
            // Rollback Transaction if Active
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }

            $_SESSION['error'] = $e->getMessage();
            header("Location: ../add-passenger.php?id=" . htmlspecialchars($bookingId));
            exit();
        }
    }

    if ($_SERVER["REQUEST_METHOD"] == "POST") {
        if (isset($_POST['deletePassenger'])) {

            try {
                if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
                    throw new Exception("Invalid CSRF token.");
                }

                $passengerId = $_POST['passengerId'];
                $bookingId = $_POST['bookingId'];

                $pdo->beginTransaction();

                $stmt = $pdo->prepare("DELETE FROM cb_tourists where tourist_id = :tourist_id");
                $stmt->execute([
                    ':tourist_id' => $passengerId
                ]);

                if ($stmt->rowCount() === 0) {
                    throw new Exception('Failed to delete.');
                }

                $pdo->commit();

                $_SESSION['success'] = "Passenger info has been Deleted";
                header("Location: ../add-passenger.php?id=" . $bookingId); // Redirect back to the form
                exit();
            } catch (Exception $e) {
                $pdo->rollBack();
                $_SESSION['error'] = $e->getMessage();
                header("Location: ../add-passenger.php?id=" . $bookingId); // Redirect back to the form
                exit();
            }
        }
    }

    if ($_SERVER["REQUEST_METHOD"] == "POST") {
        if (isset($_POST['crewPassengerBtn'])) {

            try {
                if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
                    throw new Exception("Invalid CSRF token.");
                }

                $getBookingId = $_POST['bookingId'];
                $newStatus = "Approved";

                // For checking
                $bookingDetails = getBookingDetails2($pdo, $getBookingId);
                $counts = getCrewCount($pdo, $getBookingId);

                // Ongoing Count of Passengers
                $actual_crewCount = $counts['crew'];
                $current_crewCount = $bookingDetails['total_crew'];
                $totalCrewCount = $actual_crewCount + $current_crewCount;

                $pdo->beginTransaction();

                $stmt = $pdo->prepare("UPDATE cb_booking_approvals SET boat = :boat WHERE booking_id = :booking_id");
                $stmt->execute([
                    ':boat' => $newStatus,
                    ':booking_id' => $getBookingId
                ]);

                $stmt = $pdo->prepare("UPDATE cb_payments SET total_crew = :total_crew WHERE booking_id = :booking_id");
                $stmt->execute([
                    ':total_crew' => $totalCrewCount,
                    ':booking_id' => $getBookingId
                ]);

                // Insert log entry
                $type = "Booking - " . $newStatus;
                $description = "Boat operator: " . $_SESSION['username'] . " Approved a booking: " . $referenceNumber  . ". Date Created: " . date("Y-m-d");
                $stmt = $pdo->prepare("INSERT INTO system_logs (type, description) VALUES (:type, :description)");
                $stmt->execute([
                    ':type' => $type,
                    ':description' => $description
                ]);

                $pdo->commit();

                $_SESSION['success'] = $newStatus . " successfully";
                header("Location: ../transaction-pending.php"); // Redirect back to the form
                exit();
            } catch (Exception $e) {
                $pdo->rollBack();
                $_SESSION['error'] = $e->getMessage();
                header("Location: ../add-passenger.php?id=" . $bookingId); // Redirect back to the form
                exit();
            }
        }
    }
}
